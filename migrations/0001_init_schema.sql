-- +goose Up
CREATE SCHEMA IF NOT <PERSON>XISTS datahub_receiver;

CREATE TABLE IF NOT EXISTS datahub_receiver.tb_metadata (
    s_order_id TEXT PRIMARY KEY,
    t_order_created_at TIMESTAMP NOT NULL,
    j_data JSONB,
    n_sequence INT8
);

CREATE TABLE IF NOT EXISTS datahub_receiver.tb_lock (
    s_id TEXT PRIMARY KEY,
    t_expire_time TIMESTAMP NOT NULL
);

CREATE TABLE IF NOT EXISTS datahub_receiver.tb_error (
    s_id TEXT PRIMARY KEY,
    s_type TEXT NOT NULL,
    s_key TEXT NOT NULL,
    s_order_id TEXT NOT NULL,
    n_sequence INT8 NOT NULL,
    j_handlers JSONB,
    CONSTRAINT unique_s_order_id_type_key UNIQUE (s_order_id, s_type, s_key)
);

-- +goose Down
DROP TABLE IF EXISTS datahub_receiver.tb_error;
DROP TABLE IF EXISTS datahub_receiver.tb_lock;
DROP TABLE IF EXISTS datahub_receiver.tb_metadata;
DROP SCHEMA IF EXISTS datahub_receiver;
