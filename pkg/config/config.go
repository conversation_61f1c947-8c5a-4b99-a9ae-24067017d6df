package config

import (
	"fmt"
	"strings"

	"github.com/spf13/viper"
)

// Config represents the application configuration
type Config struct {
	Postgres PostgresConfig `mapstructure:"postgres"`
	Ka<PERSON>ka    KafkaConfig    `mapstructure:"kafka"`
	Logger   LoggerConfig   `mapstructure:"logger"`
	Lock     LockConfig     `mapstructure:"lock"`
	Cronjob  CronConfig     `mapstructure:"cronjob"`
}

// PostgresConfig represents PostgreSQL configuration
type PostgresConfig struct {
	Host            string `mapstructure:"host"`
	Port            int    `mapstructure:"port"`
	User            string `mapstructure:"user"`
	Password        string `mapstructure:"password"`
	DBName          string `mapstructure:"dbname"`
	SSLMode         string `mapstructure:"sslmode"`
	MaxOpenConns    int    `mapstructure:"max_open_conns"`
	MaxIdleConns    int    `mapstructure:"max_idle_conns"`
	ConnMaxLifetime int    `mapstructure:"conn_max_lifetime"`  // in minutes
	ConnMaxIdleTime int    `mapstructure:"conn_max_idle_time"` // in minutes
}

// KafkaConfig represents Kafka configuration
type KafkaConfig struct {
	Brokers       []string `mapstructure:"brokers"`
	GroupID       string   `mapstructure:"group_id"`
	PaygateTopics []string `mapstructure:"paygate_topics"`
}

// LoggerConfig represents logger configuration
type LoggerConfig struct {
	Level string `mapstructure:"level"`
}

type LockConfig struct {
	ExpireSeconds   int `mapstructure:"expire_seconds"`
	RetryIntervalMs int `mapstructure:"retry_interval_ms"`
}

type CronConfig struct {
	Schedule string `mapstructure:"schedule"`
}

func LoadConfig(configFile string) (*Config, error) {
	viper.SetConfigFile(configFile)

	if err := viper.ReadInConfig(); err != nil {
		return nil, fmt.Errorf("error reading config file: %v", err)
	}

	var config Config
	if err := viper.Unmarshal(&config); err != nil {
		return nil, fmt.Errorf("error unmarshaling config: %v", err)
	}

	return &config, nil
}

func (c *Config) GetLoggerLevel() string {
	return strings.ToLower(c.Logger.Level)
}

func (c *Config) GetPostgresDSN() string {
	return fmt.Sprintf("host=%s port=%d user=%s password=%s dbname=%s sslmode=%s",
		c.Postgres.Host,
		c.Postgres.Port,
		c.Postgres.User,
		c.Postgres.Password,
		c.Postgres.DBName,
		c.Postgres.SSLMode,
	)
}
