package logger

// Fields Type to pass when we want to call With<PERSON>ields for structured logging
type Fields map[string]interface{}

const (
	//DebugLevel has verbose message
	DebugLevel = "debug"
	//InfoLevel is default log level
	InfoLevel = "info"
	//WarnLevel is for logging messages about possible issues
	WarnLevel = "warn"
	//ErrorLevel is for logging errors
	ErrorLevel = "error"
	//FatalLevel is for logging fatal messages. The sytem shutsdown after logging the message.
	FatalLevel = "fatal"
	//PanicLevel is for logging panic messages. The sytem shutsdown after logging the message.
	PanicLevel = "panic"
)

/*func NewChiLogger() middleware.LoggerInterface {
	return log
}*/

// Logger is our contract for the logger
type Logger interface {
	Debug(args ...interface{})

	Debugf(format string, args ...interface{})

	Info(args ...interface{})

	Infof(format string, args ...interface{})

	Warn(args ...interface{})

	Warnf(format string, args ...interface{})

	Error(args ...interface{})

	Errorf(format string, args ...interface{})

	Fatal(args ...interface{})

	Fatalf(format string, args ...interface{})

	Panic(args ...interface{})

	Panicf(format string, args ...interface{})

	WithFields(keyValues Fields) Logger

	Print(args ...interface{})
}

// Configuration stores the config for the logger
// For some loggers there can only be one level across writers, for such the level of Console is picked by default
type Configuration struct {
	EnableConsole     bool
	ConsoleJSONFormat bool
	ConsoleLevel      string
	EnableFile        bool
	FileJSONFormat    bool
	FileLevel         string
	FileLocation      string
}

type ReceiverLogger struct {
	log Logger
}

func NewReceiverLogger(level string) (*ReceiverLogger, error) {
	log, err := newZapLogger(Configuration{
		EnableConsole:     true,
		ConsoleLevel:      level,
		ConsoleJSONFormat: true,
		EnableFile:        false,
	})
	if err != nil {
		return nil, err
	}
	return &ReceiverLogger{log: log}, nil
}

func (l *ReceiverLogger) Debug(args ...interface{}) {
	l.log.Debug(args...)
}

func (l *ReceiverLogger) Debugf(format string, args ...interface{}) {
	l.log.Debugf(format, args...)
}

func (l *ReceiverLogger) Info(args ...interface{}) {
	l.log.Info(args...)
}

func (l *ReceiverLogger) Infof(format string, args ...interface{}) {
	l.log.Infof(format, args...)
}

func (l *ReceiverLogger) Warn(args ...interface{}) {
	l.log.Warn(args...)
}

func (l *ReceiverLogger) Warnf(format string, args ...interface{}) {
	l.log.Warnf(format, args...)
}

func (l *ReceiverLogger) Error(args ...interface{}) {
	l.log.Error(args...)
}

func (l *ReceiverLogger) Errorf(format string, args ...interface{}) {
	l.log.Errorf(format, args...)
}

func (l *ReceiverLogger) Fatal(args ...interface{}) {
	l.log.Fatal(args...)
}

func (l *ReceiverLogger) Fatalf(format string, args ...interface{}) {
	l.log.Fatalf(format, args...)
}

func (l *ReceiverLogger) Panic(args ...interface{}) {
	l.log.Panic(args...)
}

func (l *ReceiverLogger) Panicf(format string, args ...interface{}) {
	l.log.Panicf(format, args...)
}

func (l *ReceiverLogger) WithFields(keyValues Fields) Logger {
	return l.log.WithFields(keyValues)
}
