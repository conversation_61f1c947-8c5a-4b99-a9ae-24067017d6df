package models

import (
	"time"

	"git.onepay.vn/onepay/datahub-receiver/pkg/constant"
)

type KafkaMessage = DataMap

func (k KafkaMessage) GetOrderID() string {
	id := k.GetID()
	if id != "" {
		return id
	}

	order := k.GetNormalizedDataByType(constant.Order)
	if order == nil {
		return ""
	}

	id = order[constant.KeyMap[constant.Order]].(string)
	return id
}

func (k KafkaMessage) GetInfo() map[string]any {
	info, ok := k["Info"].(map[string]any)
	if !ok {
		return nil
	}
	return info
}

// Info.UpdatedField
func (k KafkaMessage) GetUpdatedField() string {
	info := k.GetInfo()
	if info == nil {
		return ""
	}

	field, ok := info["UpdatedField"].(string)
	if !ok {
		return ""
	}
	return field
}

func (k KafkaMessage) GetUpdatedID() string {
	info := k.GetInfo()
	if info == nil {
		return ""
	}

	updatedKey, ok := info["UpdatedKey"].(map[string]any)
	if !ok {
		return ""
	}

	updatedField := k.GetUpdatedField()

	keyName, ok := constant.KeyMap[updatedField]
	if !ok {
		return ""
	}

	updatedID, ok := updatedKey[keyName].(string)
	if !ok {
		return ""
	}
	return updatedID
}

func (k KafkaMessage) GetOrderCreatedAt() time.Time {
	rawTimeOuter, ok := k["CreatedAt"].(string)
	if ok && rawTimeOuter != "" {
		createdAt, err := time.Parse(constant.TimeLayout, rawTimeOuter)
		if err == nil {
			return createdAt
		}
	}

	order, ok := k[constant.Order].(map[string]any)
	if !ok {
		return time.Time{}
	}

	orderData, ok := order["Data"].(map[string]any)
	if !ok {
		return time.Time{}
	}

	createdAt := DataMap(orderData).GetInt64ByKey("D_CREATE")

	return time.UnixMilli(createdAt)
}

func (k KafkaMessage) ToMetadata() Metadata {
	metadata := make(map[string]any)

	updatedType := k.GetUpdatedField()
	keyName := constant.KeyMap[updatedType]
	updatedID := k.GetUpdatedID()
	sequence, _ := k.GetSequenceByTypeAndKey(updatedType, updatedID)

	data := map[string]any{
		keyName:    updatedID,
		"Sequence": sequence,
	}

	if updatedType == constant.Order {
		metadata[updatedType] = data
	} else {
		metadata[updatedType] = []map[string]any{data}
	}

	id := k.GetOrderID()
	orderCreatedAt := k.GetOrderCreatedAt()

	return Metadata{
		ID:             id,
		OrderCreatedAt: orderCreatedAt,
		Data:           DataMap(metadata),
		Sequence:       k.GetSequence(),
	}
}
