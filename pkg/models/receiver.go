package models

import (
	"time"
)

type Metadata struct {
	ID             string    `db:"s_order_id"`
	Data           DataMap   `db:"j_data"`
	OrderCreatedAt time.Time `db:"t_order_created_at"`
	Sequence       int64     `db:"n_sequence"`
}

type Lock struct {
	ID         string    `db:"s_id"`
	ExpireTime time.Time `db:"t_expire_time"`
}

type Error struct {
	ID       string   `db:"s_id"`
	Type     string   `db:"s_type"`
	Key      string   `db:"s_key"`
	Sequence int64    `db:"s_squence"`
	OrderID  string   `db:"s_order_id"`
	Handlers []string `db:"j_handlers"`
}
