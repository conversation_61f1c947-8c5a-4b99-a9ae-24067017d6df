package models

import (
	"git.onepay.vn/onepay/datahub-receiver/pkg/constant"
)

type DataMap map[string]any

func (d DataMap) GetID() string {
	id, ok := d["ID"].(string)
	if !ok {
		return ""
	}

	return id
}

func (d DataMap) GetNormalizedDataByType(mType string) map[string]any {
	data, ok := d[mType].(map[string]any)
	if !ok {
		return nil
	}

	key := constant.KeyMap[mType]

	return DataMap(data).Normalize(key)
}

func (d DataMap) GetNormalizedDataListByType(mType string) []map[string]any {
	key := constant.KeyMap[mType]

	dataArr, ok := d[mType].([]any)
	if !ok {
		return nil
	}

	res := []map[string]any{}

	for _, rawData := range dataArr {
		data, ok := rawData.(map[string]any)
		if ok {
			res = append(res, DataMap(data).Normalize(key))
		}
	}

	return res
}

func (d DataMap) Normalize(keyName string) map[string]any {
	res := make(map[string]any)

	val, ok := d[keyName]
	if ok {
		res[keyName] = val
	}
	res["Sequence"] = d.GetSequence()
	return res
}

func (d DataMap) GetSequenceByTypeAndKey(mType, key string) (int64, int) {
	if mType == constant.Order {
		data := d.GetNormalizedDataByType(mType)
		if data == nil {
			return 0, -1
		}
		return DataMap(data).GetSequence(), 0
	}

	dataArr := d.GetNormalizedDataListByType(mType)
	for i, data := range dataArr {
		dataMap := DataMap(data)
		if dataMap.GetID() == key {
			return dataMap.GetSequence(), i
		}
	}
	return 0, -1
}

func (d DataMap) GetSequence() int64 {
	return d.GetInt64ByKey("Sequence")
}

// ["Sequence"]
func (d DataMap) GetInt64ByKey(key string) int64 {
	rawSeq, ok := d[key]
	if !ok {
		return 0
	}

	var seq int64
	switch v := rawSeq.(type) {
	case int64:
		seq = v
	case int:
		seq = int64(v)
	case float64:
		seq = int64(v)
	case float32:
		seq = int64(v)
	default:
		seq = 0
	}

	return seq
}
