package application

import (
	"git.onepay.vn/onepay/datahub-receiver/internal/domain"
	"git.onepay.vn/onepay/datahub-receiver/pkg/logger"
	"git.onepay.vn/onepay/datahub-receiver/pkg/models"
	"github.com/google/uuid"
)

type HandlerEngine struct {
	messageType string
	handlers    []models.HandlerFunc
	logger      *logger.ReceiverLogger
}

var _ (domain.IHandlerEngine) = (*HandlerEngine)(nil)

func NewHandlerEngine(mType string, handlers []models.HandlerFunc, logger *logger.ReceiverLogger) *HandlerEngine {
	return &HandlerEngine{
		messageType: mType,
		handlers:    handlers,
		logger:      logger,
	}
}

func (e *HandlerEngine) GetMessageType() string {
	return e.messageType
}

func (e *HandlerEngine) ExecuteWithFunc(msg models.KafkaMessage, isExecute func(funcID string) bool) *models.Error {
	errFunc := []string{}
	for _, handler := range e.handlers {
		if isExecute(handler.ID) {
			err := handler.Function(msg)
			if err != nil {
				errFunc = append(errFunc, handler.ID)
			}
		}
	}

	if len(errFunc) == 0 {
		return nil
	}

	updatedID := msg.GetUpdatedID()
	sequence, _ := msg.GetSequenceByTypeAndKey(e.messageType, updatedID)
	orderID := msg.GetOrderID()

	errDB := models.Error{
		ID:       uuid.NewString(),
		Type:     e.messageType,
		Key:      updatedID,
		Sequence: sequence,
		OrderID:  orderID,
		Handlers: errFunc,
	}

	return &errDB
}
