package application

import (
	"context"
	"fmt"
	"time"

	"git.onepay.vn/onepay/datahub-receiver/internal/domain"
	"git.onepay.vn/onepay/datahub-receiver/pkg/config"
	"git.onepay.vn/onepay/datahub-receiver/pkg/logger"
	"git.onepay.vn/onepay/datahub-receiver/pkg/models"
)

type CommonService struct {
	metadataRepo domain.IMetadataRepo
	lockRepo     domain.ILockRepo
	errorRepo    domain.IErrorRepo
	lockConfig   config.LockConfig
	logger       *logger.ReceiverLogger
}

var _ (domain.ICommonService) = (*CommonService)(nil)

func NewCommonService(
	metadataRepo domain.IMetadataRepo,
	lockRepo domain.ILockRepo,
	errorRepo domain.IErrorRepo,
	lockConfig config.LockConfig,
	logger *logger.ReceiverLogger,
) domain.ICommonService {
	return &CommonService{
		metadataRepo: metadataRepo,
		lockRepo:     lockRepo,
		errorRepo:    errorRepo,
		lockConfig:   lockConfig,
		logger:       logger,
	}
}

func (s *CommonService) ProcessMetadata(ctx context.Context, orderID string, processor func(metadata *models.Metadata) ([]models.Error, error)) error {
	if err := s.lockID(ctx, orderID); err != nil {
		return fmt.Errorf("failed to lock order id: %s", err.Error())
	}

	// unlock id
	defer func() {
		if err := s.lockRepo.Unlock(orderID); err != nil {
			s.logger.Errorf("ProcessMetadata - failed to unlock order id: %v", err)
		}
	}()

	// metadata
	metadata, err := s.metadataRepo.GetMetadataByID(orderID)
	if err != nil {
		return fmt.Errorf("failed to get metadata from db: %v", err)
	}

	errors, err := processor(metadata)
	if err != nil {
		return fmt.Errorf("failed to process msg: %v", err)
	}

	for _, errDB := range errors {
		if saveDBErr := s.errorRepo.SaveError(errDB); saveDBErr != nil {
			return fmt.Errorf("failed to save error to db: %v", saveDBErr)
		}
	}

	return nil
}

func (s *CommonService) lockID(ctx context.Context, id string) error {
	retryInterval := s.lockConfig.RetryIntervalMs // default retry interval in ms
	lockDuration := s.lockConfig.ExpireSeconds

	ticker := time.NewTicker(time.Duration(retryInterval) * time.Millisecond)
	defer ticker.Stop()

	// check order id is locked
	for {
		locked, err := s.lockRepo.IsLocked(id)
		if err != nil {
			return fmt.Errorf("lockID - failed to check lock: %v", err)
		}
		if !locked {
			break
		}

		s.logger.Debugf("ID %s is locked", id)

		select {
		case <-ctx.Done():
			return ctx.Err()
		case <-ticker.C:
			// continue looping
		}
	}

	lockDetails := models.Lock{
		ID:         id,
		ExpireTime: time.Now().Add(time.Duration(lockDuration) * time.Second),
	}

	err := s.lockRepo.Lock(lockDetails)
	if err != nil {
		return fmt.Errorf("failed to lock id %s: %v", id, err)
	}

	return nil
}
