package application

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"git.onepay.vn/onepay/datahub-receiver/internal/domain"
	"git.onepay.vn/onepay/datahub-receiver/pkg/constant"
	"git.onepay.vn/onepay/datahub-receiver/pkg/logger"
	"git.onepay.vn/onepay/datahub-receiver/pkg/models"
)

type ReceiverService struct {
	handlerEngines []domain.IHandlerEngine
	metadataRepo   domain.IMetadataRepo
	errorRepo      domain.IErrorRepo
	logger         *logger.ReceiverLogger
	commonService  domain.ICommonService
}

func NewReceiverService(
	handlers []models.HandlerFunc,
	metadataRepo domain.IMetadataRepo,
	errorRepo domain.IErrorRepo,
	logger *logger.ReceiverLogger,
	commonService domain.ICommonService,
) domain.IReceiverService {
	var engines []domain.IHandlerEngine

	for mType := range constant.KeyMap {
		funcs := []models.HandlerFunc{}
		for i := range handlers {
			if handlers[i].Type == mType {
				funcs = append(funcs, handlers[i])
			}
		}
		newEngine := NewHandlerEngine(mType, funcs, logger)
		engines = append(engines, newEngine)
	}

	return &ReceiverService{
		handlerEngines: engines,
		metadataRepo:   metadataRepo,
		errorRepo:      errorRepo,
		logger:         logger,
		commonService:  commonService,
	}
}

func (s *ReceiverService) ProcessMessage(ctx context.Context, message []byte) error {
	var msg models.KafkaMessage
	if err := json.Unmarshal(message, &msg); err != nil {
		return fmt.Errorf("ProcessMessage - failed to unmarshal kafka message: %s", err.Error())
	}

	orderID := msg.GetOrderID()

	mType := msg.GetUpdatedField()

	handlerEngine := s.getHandlerEngine(mType)
	if handlerEngine == nil {
		return fmt.Errorf("invalid type %s", mType)
	}

	processor := func(metadata *models.Metadata) ([]models.Error, error) {
		if metadata == nil {
			newMeta := msg.ToMetadata()
			metadata = &newMeta
		}

		comRes := s.compareSequence(metadata, msg)

		// if updated
		if comRes > 0 {
			// update metadata
			if err := s.metadataRepo.SaveMetadata(*metadata); err != nil {
				return nil, fmt.Errorf("failed to save metadata: %v", err)
			}

			// call handler functions
			isExecute := func(_ string) bool { return true }

			errDB := handlerEngine.ExecuteWithFunc(msg, isExecute)
			if errDB != nil {
				return []models.Error{*errDB}, nil
			}
		}

		return nil, nil
	}

	return s.commonService.ProcessMetadata(ctx, orderID, processor)
}

func (s *ReceiverService) ProcessError(ctx context.Context) error {
	startTime := time.Now()
	defer func() {
		s.logger.Infof("Process error - took: %v", time.Since(startTime))
	}()

	errors, err := s.errorRepo.GetErrors()
	if err != nil {
		return fmt.Errorf("failed to get errors from db: %v", err)
	}

	orderIDs := []string{}
	for _, e := range errors {
		orderIDs = append(orderIDs, e.OrderID)
	}

	metadataList, err := s.metadataRepo.GetMetadataByIDs(orderIDs)
	if err != nil {
		return fmt.Errorf("failed to get metadata list from db: %v", err)
	}
	metadataMap := make(map[string]models.Metadata, len(metadataList))
	for _, m := range metadataList {
		metadataMap[m.ID] = m
	}

	toRemoves := []string{}
	for _, e := range errors {
		m, ok := metadataMap[e.OrderID]
		if !ok {
			continue
		}

		curSeq, _ := m.Data.GetSequenceByTypeAndKey(e.Type, e.ID)

		if e.Sequence < curSeq {
			toRemoves = append(toRemoves, e.ID)
		}
	}

	if len(toRemoves) > 0 {
		err := s.errorRepo.DeleteErrors(toRemoves)
		if err != nil {
			return fmt.Errorf("failed to remove errors from db: %v", err)
		}
		s.logger.Debugf("Removed %d errors", len(toRemoves))
	}

	return nil
}

func (s *ReceiverService) getHandlerEngine(mType string) domain.IHandlerEngine {
	for _, engine := range s.handlerEngines {
		if engine.GetMessageType() == mType {
			return engine
		}
	}
	return nil
}

// compare metadata with message, return  less: -1, equal: 0, greater: 1
func (s *ReceiverService) compareSequence(metadata *models.Metadata, msg models.KafkaMessage) int {
	res := 0

	mType := msg.GetUpdatedField()
	updatedKey := msg.GetUpdatedID()

	msgSeq, msgIdx := msg.GetSequenceByTypeAndKey(mType, updatedKey)
	if msgIdx < 0 {
		return res
	}

	curSeq, curIdx := metadata.Data.GetSequenceByTypeAndKey(mType, updatedKey)
	if mType == constant.Order {
		if curIdx < 0 || curSeq < msgSeq {
			newData := msg.GetNormalizedDataByType(mType)
			metadata.Data[mType] = newData
			res = 1
		} else if curSeq > msgSeq {
			res = -1
		}
	} else {
		newDataArr := msg.GetNormalizedDataListByType(mType)
		curDataArr := metadata.Data.GetNormalizedDataListByType(mType)
		if curIdx < 0 {
			curDataArr = append(curDataArr, newDataArr[msgIdx])
			res = 1
		} else if curSeq < msgSeq {
			curDataArr[curIdx] = newDataArr[msgIdx]
			res = 1
		} else if curSeq > msgSeq {
			res = -1
		}
		metadata.Data[mType] = curDataArr
	}

	return res
}
