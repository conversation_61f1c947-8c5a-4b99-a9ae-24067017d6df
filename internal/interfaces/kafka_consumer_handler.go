package interfaces

import (
	"context"

	"git.onepay.vn/onepay/datahub-receiver/internal/domain"
	"git.onepay.vn/onepay/datahub-receiver/pkg/logger"

	"github.com/twmb/franz-go/pkg/kgo"
)

type ReceiverConsumer struct {
	processor domain.IReceiverService
	client    *kgo.Client
	logger    *logger.ReceiverLogger
}

func NewReceiverConsumer(client *kgo.Client, processor domain.IReceiverService, logger *logger.ReceiverLogger) *ReceiverConsumer {
	return &ReceiverConsumer{
		client:    client,
		logger:    logger,
		processor: processor,
	}
}

func (c *ReceiverConsumer) StartConsuming(ctx context.Context) {
	defer c.client.Close()

	for {
		select {
		case <-ctx.Done():
			return
		default:
			// using PollRecords to handle limit of records
			fetches := c.client.PollRecords(ctx, 100)
			// Basic error handling
			if errs := fetches.Errors(); len(errs) > 0 {
				continue
			}

			iter := fetches.RecordIter()
			for !iter.Done() {
				record := iter.Next()

				if err := c.processor.ProcessMessage(ctx, record.Value); err != nil {
					c.logger.Errorf("get error in topic: %v, partition: %v, offset: %v, error: %v", record.Topic, record.Partition, record.Offset, err)
				} else {
					c.client.CommitRecords(ctx, record)
				}
			}
		}
	}
}
