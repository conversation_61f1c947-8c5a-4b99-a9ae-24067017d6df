package infrastructure

import (
	"encoding/json"
	"fmt"
	"strings"

	"git.onepay.vn/onepay/datahub-receiver/internal/domain"
	"git.onepay.vn/onepay/datahub-receiver/pkg/models"
)

var _ domain.IErrorRepo = (*ErrorRepo)(nil)

type ErrorRepo struct {
	pgAdapter *PostgresAdapter
}

func NewErrorRepo(pgAdapter *PostgresAdapter) *ErrorRepo {
	return &ErrorRepo{
		pgAdapter: pgAdapter,
	}
}

func (r *ErrorRepo) GetErrors() ([]models.Error, error) {
	rows, err := r.pgAdapter.ExecQuery(`
		SELECT 
			s_id, 
			s_type,
			s_key, 
			n_sequence, 
			s_order_id, 
			j_handlers
		FROM datahub_receiver.tb_error
	`)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var errors []models.Error
	for rows.Next() {
		var id, typeMsg, orderID, key string
		var sequence int64
		var handlersJSON []byte

		if err := rows.Scan(
			&id,
			&typeMsg,
			&key,
			&sequence,
			&orderID,
			&handlersJSON,
		); err != nil {
			return nil, err
		}

		var handlers []string

		if err := json.Unmarshal(handlersJSON, &handlers); err != nil {
			return nil, err
		}

		e := models.Error{
			ID:       id,
			Type:     typeMsg,
			Sequence: sequence,
			Key:      key,
			OrderID:  orderID,
			Handlers: handlers,
		}

		errors = append(errors, e)
	}

	if err = rows.Err(); err != nil {
		return nil, err
	}

	return errors, nil
}

func (r *ErrorRepo) SaveError(err models.Error) error {
	handlersJSON, marshalErr := json.Marshal(err.Handlers)
	if marshalErr != nil {
		return marshalErr
	}

	query := `
		MERGE INTO datahub_receiver.tb_error AS target
		USING (VALUES ($1, $2, $3, $4::int8, $5, $6::jsonb)) 
			AS source(s_id, s_type, s_key, n_sequence, s_order_id, j_handlers)
		ON target.s_order_id = source.s_order_id AND target.s_type = source.s_type AND target.s_key = source.s_key
		WHEN MATCHED AND target.n_sequence < source.n_sequence THEN
			UPDATE SET 
				n_sequence = source.n_sequence,
				j_handlers = source.j_handlers
		WHEN NOT MATCHED THEN
			INSERT (s_id, s_type, s_key, n_sequence, s_order_id, j_handlers)
			VALUES (source.s_id, source.s_type, source.s_key, source.n_sequence, source.s_order_id, source.j_handlers);
	`
	execErr := r.pgAdapter.Exec(
		query,
		err.ID,
		err.Type,
		err.Key,
		err.Sequence,
		err.OrderID,
		handlersJSON,
	)
	if execErr != nil {
		return execErr
	}
	return nil
}

func (r *ErrorRepo) DeleteErrors(errIDs []string) error {
	if len(errIDs) == 0 {
		return nil
	}

	args := make([]interface{}, len(errIDs))
	placeholders := make([]string, len(errIDs))

	for i, id := range errIDs {
		args[i] = id
		placeholders[i] = fmt.Sprintf("$%d", i+1)
	}

	queryPattern := `
		DELETE FROM datahub_receiver.tb_error
		WHERE s_id IN (%s)
	`
	query := fmt.Sprintf(queryPattern, strings.Join(placeholders, ","))

	err := r.pgAdapter.Exec(query, args...)
	return err
}
