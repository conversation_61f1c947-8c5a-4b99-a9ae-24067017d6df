package infrastructure

import (
	"time"

	"github.com/twmb/franz-go/pkg/kgo"
)

func NewKafkaConsumerClient(brokers []string, groupID string, topics []string) (*kgo.Client, error) {
	opts := []kgo.Opt{
		kgo.SeedBrokers(brokers...),
		kgo.ConsumerGroup(groupID),
		kgo.ConsumeTopics(topics...),
		// Increase the maximum number of bytes that can be fetched in one request
		kgo.FetchMaxBytes(10 * 1024 * 1024),
		// Increase the minimum number of bytes before the broker responds
		kgo.FetchMinBytes(1024 * 1024),
		// Increase the maximum fetch wait time
		kgo.FetchMaxWait(500 * time.Millisecond),
		// Increase the number of partitions that can be fetched in parallel
		kgo.MaxConcurrentFetches(10),
		// Increase the number of requests that can run concurrently to the broker
		kgo.ProducerBatchMaxBytes(2 * 1024 * 1024),
		// Compress data to reduce network traffic (the correct way in franz-go)
		kgo.ProducerBatchCompression(kgo.SnappyCompression()),
		// disable auto commit
		kgo.DisableAutoCommit(),
	}

	client, err := kgo.NewClient(opts...)
	if err != nil {
		return nil, err
	}
	return client, nil
}
