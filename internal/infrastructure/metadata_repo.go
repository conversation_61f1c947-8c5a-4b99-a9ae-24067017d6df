package infrastructure

import (
	"encoding/json"
	"fmt"
	"strings"

	"git.onepay.vn/onepay/datahub-receiver/internal/domain"
	"git.onepay.vn/onepay/datahub-receiver/pkg/models"

	"github.com/jackc/pgx/v5"
)

var _ domain.IMetadataRepo = (*MetadataRepo)(nil)

type MetadataRepo struct {
	pgAdapter *PostgresAdapter
}

func NewMetadataRepo(pgAdapter *PostgresAdapter) *MetadataRepo {
	return &MetadataRepo{
		pgAdapter: pgAdapter,
	}
}

func (m *MetadataRepo) GetMetadataByID(orderID string) (*models.Metadata, error) {
	query := `
		SELECT s_order_id, t_order_created_at, j_data, n_sequence
		FROM datahub_receiver.tb_metadata
		WHERE s_order_id = $1
	`
	var (
		data     []byte
		metadata models.Metadata
	)
	err := m.pgAdapter.QueryRow(query, orderID).Scan(&metadata.ID, &metadata.OrderCreatedAt, &data, &metadata.Sequence)
	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, nil
		}
		return nil, err
	}

	if err := json.Unmarshal(data, &metadata.Data); err != nil {
		return nil, fmt.Errorf("failed to unmarshal metadata: %w", err)
	}
	return &metadata, nil
}

func (m *MetadataRepo) SaveMetadata(metadata models.Metadata) error {
	// Marshal the Data field to JSON
	dataJSON, err := json.Marshal(metadata.Data)
	if err != nil {
		return fmt.Errorf("failed to marshal metadata: %v", err)
	}

	query := `
		INSERT INTO datahub_receiver.tb_metadata (
			s_order_id,
			t_order_created_at,
			j_data,
			n_sequence
		)
		VALUES ($1, $2, $3, $4)
		ON CONFLICT (s_order_id) DO UPDATE SET
			j_data = EXCLUDED.j_data,
			t_order_created_at = EXCLUDED.t_order_created_at
	`
	err = m.pgAdapter.Exec(query, metadata.ID, metadata.OrderCreatedAt, dataJSON, metadata.Sequence)
	if err != nil {
		return err
	}
	return nil
}

func (m *MetadataRepo) GetMetadataByIDs(keys []string) ([]models.Metadata, error) {
	if len(keys) == 0 {
		return []models.Metadata{}, nil
	}

	args := make([]interface{}, len(keys))
	placeholders := make([]string, len(keys))

	for i, id := range keys {
		args[i] = id
		placeholders[i] = fmt.Sprintf("$%d", i+1)
	}

	queryPattern := `
		SELECT s_order_id, t_order_created_at, j_data
		FROM datahub_receiver.tb_metadata
		WHERE s_order_id IN (%s)
	`
	query := fmt.Sprintf(queryPattern, strings.Join(placeholders, ","))

	rows, err := m.pgAdapter.ExecQuery(query, args...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var metadatas []models.Metadata
	for rows.Next() {
		var (
			data     []byte
			metadata models.Metadata
		)
		if err := rows.Scan(&metadata.ID, &metadata.OrderCreatedAt, &data); err != nil {
			return nil, err
		}

		if err := json.Unmarshal(data, &metadata.Data); err != nil {
			return nil, fmt.Errorf("failed to unmarshal metadata: %w", err)
		}

		metadatas = append(metadatas, metadata)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return metadatas, nil
}
