package infrastructure

import (
	"context"
	"fmt"
	"strings"
	"time"

	"git.onepay.vn/onepay/datahub-receiver/pkg/config"
	"git.onepay.vn/onepay/datahub-receiver/pkg/logger"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
)

type PostgresAdapter struct {
	pool   *pgxpool.Pool
	logger *logger.ReceiverLogger
}

// NewPostgresAdapter creates a new PostgresAdapter instance.
func NewPostgresAdapter(dsn string, config *config.PostgresConfig, logger *logger.ReceiverLogger) (*PostgresAdapter, error) {
	poolConfig, err := pgxpool.ParseConfig(dsn)
	if err != nil {
		return nil, fmt.Errorf("failed to parse DSN: %w", err)
	}

	// Set pool configuration
	poolConfig.MaxConns = int32(config.MaxOpenConns)
	logger.Infof("Postgres pool: MaxConns set to %d", config.MaxOpenConns)

	poolConfig.MinConns = int32(config.MaxIdleConns)
	logger.Infof("Postgres pool: MinConns set to %d", config.MaxIdleConns)

	poolConfig.MaxConnLifetime = time.Duration(config.ConnMaxLifetime) * time.Minute
	logger.Infof("Postgres pool: MaxConnLifetime set to %d minutes", config.ConnMaxLifetime)

	poolConfig.MaxConnIdleTime = time.Duration(config.ConnMaxIdleTime) * time.Minute
	logger.Infof("Postgres pool: MaxConnIdleTime set to %d minutes", config.ConnMaxIdleTime)

	pool, err := pgxpool.NewWithConfig(context.Background(), poolConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to create connection pool: %w", err)
	}

	adapter := &PostgresAdapter{
		pool:   pool,
		logger: logger,
	}

	logger.Info("Postgres connection pool initialized successfully")

	// Start pool health monitoring
	adapter.MonitorPoolHealth()

	return adapter, nil
}

func (a *PostgresAdapter) Close() {
	if a.pool != nil {
		a.pool.Close()
	}
}

// GetPoolStats returns current connection pool statistics
func (a *PostgresAdapter) GetPoolStats() *pgxpool.Stat {
	return a.pool.Stat()
}

// LogPoolStats logs the current pool statistics.
func (a *PostgresAdapter) LogPoolStats() {
	stats := a.GetPoolStats()
	totalConns := stats.TotalConns()
	idleConns := stats.IdleConns()
	acquiredConns := stats.AcquiredConns()
	constructingConns := stats.ConstructingConns()

	var warnings []string
	if idleConns == 0 && acquiredConns > 0 {
		warnings = append(warnings, "⚠️ NO_IDLE_CONNS")
	}
	if acquiredConns == totalConns {
		warnings = append(warnings, "⚠️ POOL_EXHAUSTED")
	}
	if constructingConns > 0 {
		warnings = append(warnings, "⚠️ CONSTRUCTING_CONNS")
	}

	warningStr := ""
	if len(warnings) > 0 {
		warningStr = " " + strings.Join(warnings, " ")
	}

	a.logger.Infof("Postgres pool stats - TotalConns: %d, IdleConns: %d, AcquiredConns: %d, ConstructingConns: %d%s",
		totalConns, idleConns, acquiredConns, constructingConns, warningStr)
}

// MonitorPoolHealth starts a goroutine to monitor pool health and detect leaks
func (a *PostgresAdapter) MonitorPoolHealth() {
	go func() {
		ticker := time.NewTicker(30 * time.Second) // Check every 30 seconds
		defer ticker.Stop()

		for range ticker.C {
			stats := a.GetPoolStats()
			acquiredConns := stats.AcquiredConns()
			idleConns := stats.IdleConns()

			// Log warning if most connections are acquired
			if acquiredConns > 40 && idleConns < 5 { // 80% of pool used
				a.logger.Infof("🚨 POOL HEALTH WARNING: %d/%d connections acquired, only %d idle",
					acquiredConns, stats.TotalConns(), idleConns)
			}

			// Log if all connections are acquired (potential leak)
			if acquiredConns == stats.TotalConns() {
				a.logger.Infof("🚨 CRITICAL: All %d connections acquired - possible connection leak!",
					stats.TotalConns())
			}
		}
	}()
}

// ExecQuery executes a query and returns rows.
func (a *PostgresAdapter) ExecQuery(query string, args ...any) (pgx.Rows, error) {
	rows, err := a.pool.Query(context.Background(), query, args...)

	if err != nil {
		a.logger.Infof("❌ ExecQuery ERROR after: %v", err)
		a.LogPoolStats()
		return nil, err
	}

	a.LogPoolStats()
	return rows, nil
}

// QueryRow executes a query and returns a single row.
func (a *PostgresAdapter) QueryRow(query string, args ...any) pgx.Row {
	row := a.pool.QueryRow(context.Background(), query, args...)
	a.LogPoolStats()
	return row
}

// Exec executes a query without returning rows.
func (a *PostgresAdapter) Exec(query string, args ...any) error {
	_, err := a.pool.Exec(context.Background(), query, args...)

	if err != nil {
		a.logger.Infof("❌ Exec ERROR after: %v", err)
		a.LogPoolStats()
		return err
	}

	a.LogPoolStats()
	return nil
}
