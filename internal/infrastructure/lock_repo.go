package infrastructure

import (
	"time"

	"git.onepay.vn/onepay/datahub-receiver/internal/domain"
	"git.onepay.vn/onepay/datahub-receiver/pkg/models"

	"github.com/jackc/pgx/v5"
)

var _ domain.ILockRepo = (*LockRepo)(nil)

type LockRepo struct {
	pgAdapter *PostgresAdapter
}

func NewLockRepo(pgAdapter *PostgresAdapter) *LockRepo {
	return &LockRepo{
		pgAdapter: pgAdapter,
	}
}

func (r *LockRepo) IsLocked(orderID string) (bool, error) {
	var (
		expireTime time.Time
		now        = time.Now()
	)

	query := `
		SELECT t_expire_time AT TIME ZONE 'Asia/Bangkok'
		FROM datahub_receiver.tb_lock
		WHERE s_id = $1
	`
	err := r.pgAdapter.QueryRow(query, orderID).Scan(&expireTime)
	if err != nil {
		// If no rows, not locked; if other error, return error
		if err == pgx.ErrNoRows {
			return false, nil
		}
		return false, err
	}

	return now.Before(expireTime), nil
}

func (r *LockRepo) Lock(lockDetail models.Lock) error {
	query := `
		INSERT INTO datahub_receiver.tb_lock (s_id, t_expire_time)
		VALUES ($1, $2)
		ON CONFLICT (s_id)
		DO UPDATE SET t_expire_time = EXCLUDED.t_expire_time
	`
	err := r.pgAdapter.Exec(query, lockDetail.ID, lockDetail.ExpireTime)
	if err != nil {
		return err
	}
	return nil
}

func (r *LockRepo) Unlock(orderID string) error {
	query := `
		DELETE FROM datahub_receiver.tb_lock
		WHERE s_id = $1
	`
	err := r.pgAdapter.Exec(query, orderID)
	if err != nil {
		return err
	}
	return nil
}
