package domain

import "git.onepay.vn/onepay/datahub-receiver/pkg/models"

type ILockRepo interface {
	IsLocked(orderID string) (bool, error)
	Lock(lockDetail models.Lock) error
	Unlock(orderID string) error
}

type IMetadataRepo interface {
	GetMetadataByID(orderID string) (*models.Metadata, error)
	SaveMetadata(metadata models.Metadata) error
	GetMetadataByIDs(orderIDs []string) ([]models.Metadata, error)
}

type IErrorRepo interface {
	GetErrors() ([]models.Error, error)
	SaveError(err models.Error) error
	DeleteErrors(errIDs []string) error
}
