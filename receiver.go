package receiver

import (
	"context"
	"fmt"
	"os"
	"os/signal"
	"sync"
	"syscall"

	"git.onepay.vn/onepay/datahub-receiver/internal/application"
	"git.onepay.vn/onepay/datahub-receiver/internal/domain"
	"git.onepay.vn/onepay/datahub-receiver/internal/infrastructure"
	"git.onepay.vn/onepay/datahub-receiver/internal/interfaces"
	"git.onepay.vn/onepay/datahub-receiver/pkg/config"
	"git.onepay.vn/onepay/datahub-receiver/pkg/logger"
	"git.onepay.vn/onepay/datahub-receiver/pkg/models"

	"github.com/go-co-op/gocron/v2"
)

type Receiver struct {
	processor       domain.IReceiverService
	Config          *config.Config
	Logger          *logger.ReceiverLogger
	PostgresAdapter *infrastructure.PostgresAdapter
	KafkaConsumer   *interfaces.ReceiverConsumer
}

// New initializes and returns a new Receiver instance with config, logger, Postgres, and Kafka consumer
func New(configFile string, handlers []models.HandlerFunc) (*Receiver, error) {
	cfg, err := config.LoadConfig(configFile)
	if err != nil {
		return nil, fmt.Errorf("failed to load config: %v", err)
	}

	log, err := logger.NewReceiverLogger(cfg.GetLoggerLevel())
	if err != nil {
		return nil, fmt.Errorf("failed to initialize logger: %v", err)
	}

	pgAdapter, err := infrastructure.NewPostgresAdapter(cfg.GetPostgresDSN(), &cfg.Postgres, log)
	if err != nil {
		return nil, fmt.Errorf("failed to initialize postgres adapter: %v", err)
	}

	kafkaClient, err := infrastructure.NewKafkaConsumerClient(cfg.Kafka.Brokers, cfg.Kafka.GroupID, cfg.Kafka.PaygateTopics)
	if err != nil {
		return nil, fmt.Errorf("failed to initialize kafka consumer: %v", err)
	}

	metadataRepo := infrastructure.NewMetadataRepo(pgAdapter)
	lockRepo := infrastructure.NewLockRepo(pgAdapter)
	errorRepo := infrastructure.NewErrorRepo(pgAdapter)

	commonService := application.NewCommonService(metadataRepo, lockRepo, errorRepo, cfg.Lock, log)
	service := application.NewReceiverService(handlers, metadataRepo, errorRepo, log, commonService)

	kafkaConsumer := interfaces.NewReceiverConsumer(kafkaClient, service, log)

	return &Receiver{
		processor:       service,
		Config:          cfg,
		Logger:          log,
		PostgresAdapter: pgAdapter,
		KafkaConsumer:   kafkaConsumer,
	}, nil
}

// Start consuming messages from Kafka and applies all handlers to each message
func (r *Receiver) Start() {
	defer r.PostgresAdapter.Close()

	// Create a cancellable context to manage the lifecycle of consumers and jobs
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	var wg sync.WaitGroup

	// Start Kafka consumer
	r.Logger.Info("Starting receiver consumer...")
	wg.Add(1)
	go func() {
		defer wg.Done()
		r.KafkaConsumer.StartConsuming(ctx)
	}()

	// Start a scheduler job to clean errors periodically
	wg.Add(1)
	go func() {
		defer wg.Done()
		scheduler, err := gocron.NewScheduler()
		if err != nil {
			r.Logger.Errorf("Failed to initialize scheduler: %v", err)
			return
		}
		defer scheduler.Shutdown()

		_, err = scheduler.NewJob(
			gocron.CronJob(r.Config.Cronjob.Schedule, false),
			gocron.NewTask(func() {
				if err := r.processor.ProcessError(ctx); err != nil {
					r.Logger.Errorf("Error running CleanError: %v", err)
				}
			}),
		)
		if err != nil {
			r.Logger.Errorf("Failed to set new job: %v", err)
			return
		}

		scheduler.Start()

		// Wait until context is cancelled
		<-ctx.Done()
	}()

	// Set up channel to catch termination signals from the operating system
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	// Wait for termination signal
	sig := <-sigChan
	r.Logger.Infof("Received signal: %v, shutting down...", sig)

	// Cancel context to notify all goroutines to stop
	cancel()

	// Wait for all goroutines to finish
	wg.Wait()
}
