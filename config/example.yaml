postgres:
  host: "************"
  port: 5432
  user: "ads"
  password: "ads"
  dbname: "ads"
  sslmode: "disable"
  max_open_conns: 50
  max_idle_conns: 15
  conn_max_lifetime: 60  # minutes
  conn_max_idle_time: 30 # minutes

kafka:
  brokers:
    - "************:9092"
    - "************:9092"
    - "************:9092"
  group_id: "datahub_receiver"
  # Add topics for other consumers
  paygate_topics:
    - "uat-db-112.EVENTS.OUTPUT_2"

logger:
  level: "debug" # 

lock:
  expire_seconds: 60      # Time in seconds before a lock expires
  retry_interval_ms: 100  # Interval in milliseconds to retry if locked

cronjob:
  schedule: "*/3 * * * *"  # TODO: Update

