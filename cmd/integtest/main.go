package main

import (
	"fmt"

	receiver "git.onepay.vn/onepay/datahub-receiver"
	"git.onepay.vn/onepay/datahub-receiver/pkg/constant"
	"git.onepay.vn/onepay/datahub-receiver/pkg/models"
)

func main() {
	configFile := "./config/example.yaml"

	handlers := []models.HandlerFunc{{
		ID:       "Test",
		Function: handlerTest,
		Type:     constant.Payment,
	}, {
		ID:       "Test2",
		Function: handlerTest,
		Type:     constant.Order,
	}}

	receiver, err := receiver.New(configFile, handlers)
	if err != nil {
		fmt.Println("Failed to init:", err)
	}

	receiver.Start()
}

func handlerTest(msg models.KafkaMessage) error {
	payments := msg.GetNormalizedDataListByType(constant.Payment)
	if len(payments) > 0 {
		if models.DataMap(payments[0]).GetSequence()%2 == 1 {
			return fmt.Errorf("test error")
		}
	}
	return nil
}
